package com.example.pressing.controller;

import com.example.pressing.enumeration.ArticleType;
import com.example.pressing.entity.Commande;
import com.example.pressing.service.CommandeService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/commandes")
@RequiredArgsConstructor
public class CommandeController {
    private final CommandeService commandeService;

    @PostMapping
    public Commande createCommande(
            @RequestParam Long clientId,
            @RequestBody Map<ArticleType, Integer> articles,
            @RequestParam(required = false) String notes) {
        return commandeService.createCommande(clientId, articles, notes);
    }

    @GetMapping("/client/{clientId}")
    public List<Commande> getCommandesByClient(@PathVariable Long clientId) {
        return commandeService.getCommandesByClient(clientId);
    }

    @GetMapping("/statut/{statut}")
    public List<Commande> getCommandesByStatut(@PathVariable String statut) {
        return commandeService.getCommandesByStatut(statut);
    }

    @PatchMapping("/{commandeId}/statut")
    public Commande updateStatut(
            @PathVariable Long commandeId,
            @RequestParam String statut) {
        return commandeService.updateStatut(commandeId, statut);
    }

    @GetMapping("/periode")
    public List<Commande> getCommandesBetweenDates(
            @RequestParam LocalDateTime start,
            @RequestParam LocalDateTime end) {
        return commandeService.getCommandesBetweenDates(start, end);
    }
}