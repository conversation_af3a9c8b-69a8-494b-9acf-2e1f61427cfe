package com.example.pressing.controller;

import com.example.pressing.entity.Client;
import com.example.pressing.service.ClientService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/clients")
@RequiredArgsConstructor
public class ClientController {
    private final ClientService clientService;

    @PostMapping
    public Client createClient(@RequestBody Client client) {
        return clientService.createClient(client);
    }

    @GetMapping("/{id}")
    public Client getClient(@PathVariable Long id) {
        return clientService.getClientById(id);
    }

    @GetMapping("/search")
    public List<Client> searchClients(@RequestParam String nom) {
        return clientService.searchClients(nom);
    }

    @GetMapping("/phone/{telephone}")
    public Client getClientByPhone(@PathVariable String telephone) {
        return clientService.getClientByPhone(telephone);
    }

    @GetMapping
    public List<Client> getAllClients() {
        return clientService.getAllClients();
    }
}