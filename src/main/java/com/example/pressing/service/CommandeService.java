package com.example.pressing.service;

import com.example.pressing.entity.*;
import com.example.pressing.enumeration.ArticleType;
import com.example.pressing.repository.CommandeRepository;
import com.example.pressing.repository.LigneCommandeRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class CommandeService {
    private final CommandeRepository commandeRepository;
    private final LigneCommandeRepository ligneCommandeRepository;
    private final ClientService clientService;

    public Commande createCommande(Long clientId, Map<ArticleType, Integer> articles, String notes) {
        Client client = clientService.getClientById(clientId);

        Commande commande = Commande.builder()
                .client(client)
                .notes(notes)
                .build();

        commande = commandeRepository.save(commande);

        double total = 0;
        for (Map.Entry<ArticleType, Integer> entry : articles.entrySet()) {
            LigneCommande ligne = LigneCommande.builder()
                    .article(entry.getKey())
                    .quantite(entry.getValue())
                    .prixUnitaire(entry.getKey().getPrix())
                    .commande(commande)
                    .build();

            ligneCommandeRepository.save(ligne);
            total += entry.getKey().getPrix() * entry.getValue();
        }

        commande.setTotal(total);
        return commandeRepository.save(commande);
    }

    public List<Commande> getCommandesByClient(Long clientId) {
        return commandeRepository.findByClientId(clientId);
    }

    public List<Commande> getCommandesByStatut(String statut) {
        return commandeRepository.findByStatut(statut);
    }

    public Commande updateStatut(Long commandeId, String nouveauStatut) {
        Commande commande = commandeRepository.findById(commandeId).orElseThrow();
        commande.setStatut(nouveauStatut);
        return commandeRepository.save(commande);
    }

    public List<Commande> getCommandesBetweenDates(LocalDateTime start, LocalDateTime end) {
        return commandeRepository.findBetweenDates(start, end);
    }
}