package com.example.pressing.service;

import com.example.pressing.entity.Client;
import com.example.pressing.repository.ClientRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class ClientService {
    private final ClientRepository clientRepository;

    public Client createClient(Client client) {
        return clientRepository.save(client);
    }

    public Client getClientById(Long id) {
        return clientRepository.findById(id).orElseThrow();
    }

    public List<Client> searchClients(String nom) {
        return clientRepository.findByNomContainingIgnoreCase(nom);
    }

    public Client getClientByPhone(String telephone) {
        return clientRepository.findByTelephone(telephone);
    }

    public List<Client> getAllClients() {
        return clientRepository.findAll();
    }
}